"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { getTranslation } from "../i18n/translations";
import { useAuth } from "../context/ApiAuthContext";
import { signupService } from "../services/signupService";
import { useBrowserCompatibility } from "../context/BrowserCompatibilityContext";
import AssistantChat, { assistantHandlers } from "./AssistantCore";
import DynamicAvatar from "./AssistantAvatar";
import { AgentRoleEnum } from "../types/enums";
import { TIMER_CONFIG, AGENT_ROLES, STORAGE_KEYS, AUDIO_CONFIG, API_BASE_URL } from "../config/appConfig";
import WebSocketControls, { webSocketUtils } from "./AssistantControls";
import { updateRemainingTime } from "../services/api";
import CommonFeedbackModal from "./CommonFeedbackModal";
import * as audioUtils from '../utils/audioStreamingStatic';
import { requestWakeLock, releaseWakeLock } from '../utils/wakeLock';

// Component for PPCA chat screen (fifth screen in the flow)
// OPTIMIZED: Fixed multiple WebSocket interference issues:
// 1. Debounced time updates to prevent API call conflicts with WebSocket
// 2. Consolidated page unload handlers to prevent duplicate events
// 3. Optimized audio level simulation intervals
// 4. Centralized time update system for better reliability
export default function PPCAchat({
  lng,
  avatarGender,
  onComplete
}: {
  lng: string;
  avatarGender: "male" | "female";
  onComplete: () => void;
}) {
  const { user, logout, refreshUserProfile } = useAuth();
  // Use browser compatibility context instead of local state
  const { isBrowserCompatible, hasMicrophonePermission, error, clearError } = useBrowserCompatibility();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [assistantState, setAssistantState] = useState<'getting_started' | 'speaking' | 'ready'>('getting_started');
  const isPausedRef = useRef<boolean>(false); // Ref to track paused state for timer
  const isConnectedRef = useRef<boolean>(false); // Ref to track connection state for timer
  const isMicrophoneEnabledRef = useRef<boolean>(false); // Ref to track microphone state for timer
  const timeRemainingRef = useRef<number>(TIMER_CONFIG.DEFAULT_TIME); // Ref to track time remaining for cleanup
  const isUnmountingRef = useRef<boolean>(false); // Ref to track if component is unmounting
  const timeUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Ref for debounced time updates
  const [isFirstSentenceMuted, setIsFirstSentenceMuted] = useState<boolean>(true); // Track if first sentence should mute mic
  const [isCommonFeedbackModalOpen, setIsCommonFeedbackModalOpen] = useState<boolean>(false); // Track Common Feedback modal state
  const [isRestoringFromCommonFeedback, setIsRestoringFromCommonFeedback] = useState<boolean>(false); // Track restoration process

  // We still need audioLevel for the avatar
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [totalTime, setTotalTime] = useState<number>(TIMER_CONFIG.DEFAULT_TIME);
  const [timeRemaining, setTimeRemaining] = useState<number>(TIMER_CONFIG.DEFAULT_TIME);
  const assistantChatRef = useRef<any>(null);

  // Handle Lottie reference
  const handleLottieRef = useCallback((instance: any) => {
    console.log("Lottie instance received in PPCAchat:", instance);
    // We don't need to store the instance anymore
  }, []);

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Debounced time update function to prevent frequent API calls that could interfere with WebSocket
  // This solves the issue where multiple time updates could cause WebSocket connection problems
  const debouncedTimeUpdate = useCallback((remainingTimeInSeconds: number, immediate: boolean = false) => {
    // Clear any existing timeout
    if (timeUpdateTimeoutRef.current) {
      clearTimeout(timeUpdateTimeoutRef.current);
      timeUpdateTimeoutRef.current = null;
    }

    // Always save to localStorage immediately for backup
    try {
      const userId = user?.id;
      const userBackupKey = userId ? `backup_remaining_time_user_${userId}` : 'backup_remaining_time';
      localStorage.setItem(userBackupKey, remainingTimeInSeconds.toString());
      console.log(`PPCAchat: Backup saved: ${remainingTimeInSeconds} seconds with key: ${userBackupKey}`);
    } catch (error) {
      console.error('PPCAchat: Error saving backup:', error);
    }

    // If immediate update is requested or component is unmounting, update immediately
    if (immediate || isUnmountingRef.current) {
      updateTimeToBackend(remainingTimeInSeconds);
      return;
    }

    // Otherwise, debounce the API call to avoid interfering with WebSocket operations
    timeUpdateTimeoutRef.current = setTimeout(() => {
      updateTimeToBackend(remainingTimeInSeconds);
      timeUpdateTimeoutRef.current = null;
    }, 2000); // 2 second delay to avoid conflicts with WebSocket operations
  }, [user?.id]);

  // Helper function to update time to backend
  const updateTimeToBackend = useCallback(async (remainingTimeInSeconds: number) => {
    try {
      const token = localStorage.getItem('token');
      const userId = user?.id;

      if (!token || !userId) {
        console.log('PPCAchat: No token/userId found, skipping API call');
        return;
      }

      console.log(`PPCAchat: Updating remaining time to ${remainingTimeInSeconds} seconds (${Math.floor(remainingTimeInSeconds / 60)} minutes and ${remainingTimeInSeconds % 60} seconds)`);

      // Use our Next.js API route instead of direct backend call
      const response = await fetch('/api/users/me/time-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ 
          remaining_time: remainingTimeInSeconds,
          userId: userId
        })
      });

      if (response.ok) {
        console.log('PPCAchat: Successfully updated remaining time');
        // Clear the user-specific backup if the API call succeeds
        const userBackupKey = userId ? `backup_remaining_time_user_${userId}` : 'backup_remaining_time';
        localStorage.removeItem(userBackupKey);
      } else {
        console.log(`PPCAchat: API responded with status ${response.status}`);
        // Keep the backup in localStorage in case of failure
      }
    } catch (error) {
      console.error('PPCAchat: Error updating time to backend:', error);
    }
  }, [user?.id]);

  // Effect to handle audio level simulation when recording - optimized to reduce CPU usage
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRecording && isConnected) { // Only simulate when recording AND connected
      // Simulate audio levels when recording - reduced frequency to 300ms for better performance
      interval = setInterval(() => {
        // Generate random audio level between 0.3 and 0.8
        const randomLevel = 0.3 + Math.random() * 0.5;
        setAudioLevel(randomLevel);
      }, 300); // Increased from 200ms to 300ms to reduce CPU usage
    } else {
      // Reset audio level when not recording
      setAudioLevel(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, isConnected]); // Added isConnected dependency

  // We're now using the global BrowserCompatibilityContext instead of local checks

  // Effect to update connection status
  useEffect(() => {
    const updateConnectionStatus = () => {
      const indicator = document.getElementById('connection-indicator');
      const status = document.getElementById('connection-status');

      if (!indicator || !status) return;

      // Check if window.assistantSocket exists and get its state
      const socket = (window as any).assistantSocket;

      if (socket && socket.readyState === WebSocket.OPEN) {
        // Connected
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-green-500';
        indicator.innerHTML = '';
        status.textContent = t("connected") || "Connected";
        status.className = 'font-medium text-green-700';
        // Only log if connection state changes to reduce noise
        if (!isConnectedRef.current) {
          console.log('PPCAchat: WebSocket is OPEN, setting isConnected to true');
        }
        setIsConnected(true);
        // Clear any WebSocket-related errors
        if (error && error.includes("communication")) {
          clearError();
        }
      } else if (socket && socket.readyState === WebSocket.CONNECTING) {
        // Connecting
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500 relative';
        indicator.innerHTML = '<span class="absolute inset-0 rounded-full bg-yellow-500 animate-ping opacity-75"></span>';
        status.textContent = t("connecting") || "Connecting...";
        status.className = 'font-medium text-yellow-700';
        console.log('PPCAchat: WebSocket is CONNECTING, setting isConnected to false');
        setIsConnected(false);
      } else {
        // Disconnected or error - show "Please wait..." instead
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500';
        indicator.innerHTML = '';
        status.textContent = "Please wait...";
        status.className = 'font-medium text-yellow-700';
        setIsConnected(false);
      }
    };

    // Update immediately and periodically
    updateConnectionStatus();
    const interval = setInterval(updateConnectionStatus, 1000);

    // Update when window gets focus
    window.addEventListener('focus', updateConnectionStatus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', updateConnectionStatus);
    };
  }, []);

  // Update ref when isConnected state changes
  useEffect(() => {
    isConnectedRef.current = isConnected; // Update the ref for the timer
  }, [isConnected]);

  // Update ref when isPaused state changes
  useEffect(() => {
    // Update the ref for the timer
    isPausedRef.current = isPaused;
  }, [isPaused]);

  // Monitor assistant speaking state to update UI accordingly and handle first sentence muting
  useEffect(() => {
    if (!isConnected) return;

    const monitorAssistantSpeaking = async () => {
      try {
        const audioUtils = await import('../utils/audioStreamingStatic');
        const isCurrentlySpeaking = audioUtils.getAssistantSpeakingState();

        // Update speaking state for avatar
        setIsSpeaking(isCurrentlySpeaking);

        // Handle first sentence microphone muting logic (PPCA-specific)
        if (isFirstSentenceMuted && isCurrentlySpeaking) {
          // Assistant is speaking first sentence - ensure microphone is muted
          console.log('PPCAchat: Assistant speaking first sentence - keeping microphone muted');
          if (isRecording) {
            // Force mute the microphone during first sentence
            audioUtils.muteMicrophone();
            setIsRecording(false);
            isMicrophoneEnabledRef.current = false;
          }
        } else if (isFirstSentenceMuted && !isCurrentlySpeaking && assistantState === 'speaking') {
          // Assistant finished first sentence - unmute microphone
          console.log('PPCAchat: Assistant finished first sentence - unmuting microphone');
          setIsFirstSentenceMuted(false);
          audioUtils.unmuteMicrophone();
          setIsRecording(true);
          isMicrophoneEnabledRef.current = true;
          setAssistantState('ready');
        }

        // Update assistant state based on actual speaking status
        if (isCurrentlySpeaking && assistantState === 'getting_started') {
          console.log('PPCAchat: Assistant actually started speaking - updating UI to "speaking"');
          setAssistantState('speaking');
        }
      } catch (err) {
        console.error('Error monitoring assistant speaking state:', err);
      }
    };

    // Monitor every 3000ms when connected
    const speakingMonitor = setInterval(monitorAssistantSpeaking, 3000);

    return () => clearInterval(speakingMonitor);
  }, [isConnected, assistantState, isFirstSentenceMuted, isRecording]);

  // Check if user profile needs refresh after backup restoration
  useEffect(() => {
    const needsRefresh = localStorage.getItem('user_profile_needs_refresh');
    if (needsRefresh === 'true' && refreshUserProfile) {
      console.log('PPCAchat: User profile needs refresh after backup restoration');
      localStorage.removeItem('user_profile_needs_refresh');
      refreshUserProfile().then(() => {
        console.log('PPCAchat: User profile refreshed successfully');
      }).catch((error) => {
        console.error('PPCAchat: Failed to refresh user profile:', error);
      });
    }
  }, [refreshUserProfile]);

  // Wake lock effect - prevent screen auto-lock on mobile/tablet devices
  useEffect(() => {
    let wakeLockRequested = false;

    const handleWakeLock = async () => {
      // Request wake lock when connected to prevent screen auto-lock during conversation
      if (isConnected && !isPaused) {
        if (!wakeLockRequested) {
          const success = await requestWakeLock('PPCAchat');
          if (success) {
            wakeLockRequested = true;
            console.log('PPCAchat: Wake lock acquired to prevent screen auto-lock during conversation');
          }
        }
      } else {
        // Release wake lock when disconnected or paused
        if (wakeLockRequested) {
          await releaseWakeLock('PPCAchat');
          wakeLockRequested = false;
          console.log('PPCAchat: Wake lock released');
        }
      }
    };

    handleWakeLock();

    // Cleanup function to release wake lock when component unmounts
    return () => {
      if (wakeLockRequested) {
        releaseWakeLock('PPCAchat');
        console.log('PPCAchat: Wake lock released on component unmount');
      }
    };
  }, [isConnected, isPaused]);

  // Set up timer with max time of 60 minutes and remaining time from user profile
  useEffect(() => {
    // Always set the total time to the maximum of 60 minutes (3600 seconds)
    const maxTimeInSeconds = TIMER_CONFIG.MAX_TIME; // 60 minutes in seconds
    setTotalTime(maxTimeInSeconds);

    // Get user's remaining time in seconds directly from the backend
    if (user && 'remaining_time' in user) {
      // Backend now provides time in seconds, so we use it directly
      const timeInSeconds = user.remaining_time as number;
      const remainingTimeInSeconds = Math.min(timeInSeconds, maxTimeInSeconds); // Cap at MAX_TIME
      console.log(`PPCAchat: Using user's remaining time from API: ${Math.floor(timeInSeconds / 60)} minutes and ${timeInSeconds % 60} seconds (${remainingTimeInSeconds} seconds total) out of ${maxTimeInSeconds} seconds max`);
      setTimeRemaining(remainingTimeInSeconds);
      // Also update the ref
      timeRemainingRef.current = remainingTimeInSeconds;
    } else {
      // If no remaining_time is available, default to the maximum time
      console.log(`No remaining_time found, using maximum time: ${maxTimeInSeconds} seconds`);
      setTimeRemaining(maxTimeInSeconds);
      // Also update the ref
      timeRemainingRef.current = maxTimeInSeconds;
    }
  }, [user]);



  // Safe logout function - ensures timer is updated and WebSocket is closed before logout
  const handleSafeLogout = useCallback(async () => {
    console.log('PPCAchat: Safe logout initiated');

    // Create a loading state indicator for the user
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]';
    loadingIndicator.innerHTML = `
      <div class="bg-white p-4 rounded-lg shadow-lg text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
        <p class="text-gray-800">${lng === "de" ? "Speichere Daten..." : "Saving data..."}</p>
      </div>
    `;
    document.body.appendChild(loadingIndicator);

    try {
      // Use immediate time update for logout since it's critical
      const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);
      debouncedTimeUpdate(remainingTimeInSeconds, true); // Use immediate update for logout

      // Wait a moment for the update to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Now set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;
      console.log('PPCAchat: Setting unmounting flag before logout');

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      // Close WebSocket connection
      if (assistantChatRef.current) {
        console.log('PPCAchat: Closing WebSocket connection before logout');
        assistantChatRef.current.endConversation();
      }

      // Wait a moment to ensure WebSocket is fully closed
      await new Promise(resolve => setTimeout(resolve, 300));

      // Now it's safe to logout
      console.log('PPCAchat: Timer updated, WebSocket closed, proceeding with logout');

      // Remove the loading indicator
      document.body.removeChild(loadingIndicator);

      // Perform logout
      await logout();
    } catch (error) {
      console.error('PPCAchat: Error during safe logout process:', error);

      // Remove the loading indicator
      if (document.body.contains(loadingIndicator)) {
        document.body.removeChild(loadingIndicator);
      }

      // Show error message to user
      alert(lng === "de"
        ? "Es gab ein Problem beim Speichern Ihrer Daten. Bitte versuchen Sie es später erneut."
        : "There was a problem saving your data. Please try again later.");

      // If there's an error, still try to logout
      try {
        // Set unmounting flags before logout
        isUnmountingRef.current = true;
        audioUtils.setUnmountingFlag(true);

        // Close WebSocket connection
        if (assistantChatRef.current) {
          assistantChatRef.current.endConversation();
        }

        // Wait a moment to ensure WebSocket is fully closed
        await new Promise(resolve => setTimeout(resolve, 300));

        await logout();
      } catch (logoutError) {
        console.error('PPCAchat: Error during logout after safe logout failure:', logoutError);
        // Force logout by clearing localStorage and redirecting
        localStorage.clear();
        window.location.href = `/${lng}/login`;
      }
    }
  }, [logout, lng]);

  // Handle finish button click
  const handleFinish = useCallback(() => {
    if (isSubmitting) return;

    setIsSubmitting(true);

    // Set the unmounting flag to prevent new connections
    isUnmountingRef.current = true;
    console.log('PPCAchat: Setting unmounting flag before finishing');

    // Send voice logs before finishing conversation
    try {
      import('../utils/voiceLogging').then(({ endVoiceLoggingSession }) => {
        endVoiceLoggingSession('conversation_exit').catch(err => {
          console.log('Error ending voice logging session:', err);
        });
      }).catch(err => {
        console.log('Error importing voice logging during finish:', err);
      });
    } catch (err) {
      console.log('Error with voice logging during finish:', err);
    }

    // Set the unmounting flag in audioUtils to prevent new operations
    audioUtils.setUnmountingFlag(true);

    // Use immediate time update for finish operation since it's critical
    const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);
    debouncedTimeUpdate(remainingTimeInSeconds, true); // Use immediate update for finish

    // First, ensure WebSocket is fully closed
    const closeWebSocketAndProceed = () => {
      // Close WebSocket connection
      if (assistantChatRef.current) {
        console.log('PPCAchat: Closing WebSocket connection before finishing');

        // Stop all audio playback
        if (typeof window !== 'undefined' && (window as any).audioUtils) {
          console.log('PPCAchat: Stopping all audio playback');
          (window as any).audioUtils.stopAllAudio();
        }

        // End the conversation safely
        try {
          console.log('PPCAchat: Safely ending conversation');
          if (assistantChatRef.current && typeof assistantChatRef.current.endConversation === 'function') {
            assistantChatRef.current.endConversation();
          }
        } catch (err) {
          console.log('PPCAchat: Error while ending conversation, continuing with cleanup:', err);
        }

        // Force close any remaining connections
        if (typeof window !== 'undefined') {
          try {
            // Clean up global socket if it exists
            if ((window as any).assistantSocket) {
              console.log('PPCAchat: Force closing any remaining WebSocket connections');
              try {
                (window as any).assistantSocket.close(1000, 'User finished conversation');
              } catch (e) {
                // Ignore errors during close
              }
              (window as any).assistantSocket = null;
            }

            // Clean up any other audio or WebSocket resources
            if ((window as any).audioContext) {
              try {
                console.log('PPCAchat: Cleaning up audio context');
                (window as any).audioContext.close();
              } catch (e) {
                // Ignore errors during close
              }
            }
          } catch (err) {
            console.log('PPCAchat: Error during global cleanup, continuing:', err);
          }
        }
      }

      // Simply notify parent component to move to feedback screen
      // Time update is already handled by debouncedTimeUpdate above
      if (onComplete) {
        onComplete();
      }
      setIsSubmitting(false);
    };

    // Give a small delay to ensure WebSocket is properly closed
    setTimeout(closeWebSocketAndProceed, 100);

  }, [onComplete, isSubmitting]); // Removed timeRemaining from deps since we're using the ref

  // Handle Common Feedback button click
  const handleCommonFeedbackClick = useCallback(() => {
    console.log('PPCAchat: Common Feedback button clicked');

    try {
      // IMMEDIATE AUDIO STOPPING - Do this first before anything else
      console.log('PPCAchat: IMMEDIATELY stopping all PPCA audio for Common Feedback transition');

      // 1. Immediately mute the gain node to silence any ongoing audio
      try {
        audioUtils.setAudioVolume(0);
        console.log('PPCAchat: Immediately muted gain node');
      } catch (err) {
        console.error('PPCAchat: Error muting gain node:', err);
      }

      // 2. Immediately clear audio queue and stop current sources
      audioUtils.clearAudioQueue();
      console.log('PPCAchat: Immediately cleared PPCA audio queue');

      // 3. Force stop any assistant speaking state
      try {
        // Access the internal state to force stop assistant speaking
        const audioUtilsModule = audioUtils as any;
        if (audioUtilsModule.isAssistantSpeaking !== undefined) {
          audioUtilsModule.isAssistantSpeaking = false;
        }
        console.log('PPCAchat: Force stopped assistant speaking state');
      } catch (err) {
        console.error('PPCAchat: Error force stopping assistant speaking:', err);
      }

      // 3. Send interrupt message to stop any ongoing server audio generation
      if (assistantChatRef.current && assistantChatRef.current.assistantSocket) {
        const socket = assistantChatRef.current.assistantSocket;
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({
              type: 'interrupt_conversation',
              stop_audio: true,
              reason: 'common_feedback_transition',
              timestamp: Date.now()
            }));
            console.log('PPCAchat: Sent interrupt message to stop server audio generation');
          } catch (err) {
            console.error('PPCAchat: Error sending interrupt message:', err);
          }
        }
      }

      // Stop recording completely
      if (isRecording) {
        audioUtils.stopRecording();
        setIsRecording(false);
        console.log('PPCAchat: Completely stopped recording for Common Feedback transition');
      }

      // Mute microphone to prevent interference
      try {
        audioUtils.muteMicrophone();
        console.log('PPCAchat: Muted microphone for Common Feedback transition');
      } catch (err) {
        console.error('PPCAchat: Error muting microphone:', err);
      }

      // Use the same logic as the "Close WebSocket" button to properly stop PPCA
      try {
        console.log('PPCAchat: Using handleStopClick logic to properly stop PPCA for Common Feedback transition');
        assistantHandlers.handleStopClick(
          assistantChatRef,
          isPaused,
          isUnmountingRef,
          'PPCAchat',
          (newPausedState) => {
            setIsPaused(newPausedState);
            isPausedRef.current = newPausedState;
            console.log('PPCAchat: PPCA properly stopped using handleStopClick logic');
          },
          // Custom callback for Common Feedback transition
          () => {
            // Use debounced time update to prevent WebSocket interference
            const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);
            debouncedTimeUpdate(remainingTimeInSeconds, false);
            console.log('PPCAchat: Time updated during Common Feedback transition');
          }
        );
      } catch (err) {
        console.error('PPCAchat: Error using handleStopClick for Common Feedback transition:', err);
      }

      // Pause timer by setting microphone as disabled (timer only runs when mic is enabled)
      isMicrophoneEnabledRef.current = false;

      // Set paused state for UI
      setIsPaused(true);
      isPausedRef.current = true;

      // Open the Common Feedback modal
      setIsCommonFeedbackModalOpen(true);

    } catch (error) {
      console.error('PPCAchat: Error during Common Feedback transition:', error);
    }
  }, [isRecording]);

  // Handle Common Feedback modal close
  const handleCommonFeedbackModalClose = useCallback(() => {
    console.log('PPCAchat: Common Feedback modal closed');
    setIsCommonFeedbackModalOpen(false);
    setIsRestoringFromCommonFeedback(true); // Set restoration flag

    // Clear any remaining audio from Common Feedback
    audioUtils.clearAudioQueue();
    console.log('PPCAchat: Cleared audio queue after Common Feedback close');

    // Restore audio volume to normal levels
    audioUtils.setAudioVolume(2.5); // Standard volume level
    console.log('PPCAchat: Restored audio volume after Common Feedback close');

    // Clear the global WebSocket to ensure clean state
    if ((window as any).assistantSocket) {
      console.log('PPCAchat: Clearing global WebSocket before PPCA restoration');
      (window as any).assistantSocket = null;
    }

    // Since we used handleStopClick to pause the conversation, we need to properly restore the WebSocket
    try {
      console.log('PPCAchat: Starting PPCA WebSocket restoration after Common Feedback');

      // Force a fresh WebSocket connection since Common Feedback may have interfered
      if (assistantChatRef.current) {
        // First ensure any existing connection is closed
        if (assistantChatRef.current.assistantSocket) {
          try {
            assistantChatRef.current.assistantSocket.close(1000, 'Restoring PPCA connection');
          } catch (err) {
            console.log('PPCAchat: Error closing existing socket during restoration:', err);
          }
        }

        // Wait a moment for cleanup, then start fresh connection
        setTimeout(async () => {
          if (isUnmountingRef.current) {
            console.log('PPCAchat: Component unmounting, skipping restoration');
            setIsRestoringFromCommonFeedback(false);
            return;
          }

          try {
            console.log('PPCAchat: Starting fresh PPCA WebSocket connection');
            await assistantChatRef.current.startConnection();

            // Update states after successful connection
            setIsPaused(false);
            isPausedRef.current = false;
            isMicrophoneEnabledRef.current = true;

            // Ensure assistant state is ready for interaction
            if (assistantState !== 'ready') {
              setAssistantState('ready');
              console.log('PPCAchat: Set assistant state to ready after restoration');
            }

            // Clear restoration flag
            setIsRestoringFromCommonFeedback(false);
            console.log('PPCAchat: PPCA WebSocket restoration completed successfully');

          } catch (err) {
            console.error('PPCAchat: Failed to restore PPCA WebSocket connection:', err);
            setIsRestoringFromCommonFeedback(false);
            // Try using the reconnect handler as fallback
            assistantHandlers.handleReconnectClick(
              assistantChatRef,
              true, // Force paused state for reconnect
              isUnmountingRef,
              'PPCAchat',
              (newPausedState) => {
                setIsPaused(newPausedState);
                isPausedRef.current = newPausedState;
                isMicrophoneEnabledRef.current = true;
                if (assistantState !== 'ready') {
                  setAssistantState('ready');
                }
                console.log('PPCAchat: Fallback reconnection completed');
              }
            );
          }
        }, 500); // Give time for Common Feedback cleanup
      }
    } catch (err) {
      console.error('PPCAchat: Error during PPCA restoration:', err);
      setIsRestoringFromCommonFeedback(false);
    }
  }, [assistantState, isPaused]);

  // Handle Common Feedback submission complete
  const handleCommonFeedbackComplete = useCallback(() => {
    console.log('PPCAchat: Common Feedback submission completed');
    // Close the modal and resume PPCA
    handleCommonFeedbackModalClose();
  }, [handleCommonFeedbackModalClose]);

  // We're now using the global BrowserCompatibilityContext for microphone permission checks

  // Timer effect
  useEffect(() => {
    // Timer effect running

    // Start a countdown timer
    const timer = setInterval(() => {
      // Only decrement the timer if ALL conditions are met:
      // 1. WebSocket is connected (isConnectedRef.current)
      // 2. Not paused by user (isPausedRef.current)
      // 3. Microphone is enabled - for PPCA, this ensures timer only starts after first assistant message
      // Use the refs instead of the states to avoid closure issues
      if (isConnectedRef.current && !isPausedRef.current && isMicrophoneEnabledRef.current) {
        // Timer ticking - all conditions met for PPCA conversation
        setTimeRemaining(prev => {
          // Ensure we're always counting down, not up
          const newValue = prev - 1;

          // Check if timer has reached zero
          if (newValue <= 0) {
            clearInterval(timer);
            // Auto-complete when timer reaches zero
            handleFinish();
            return 0;
          }

          // Ensure we never go below zero or above totalTime
          const validValue = Math.max(0, Math.min(newValue, totalTime));
          // Update the ref with the current value for cleanup
          timeRemainingRef.current = validValue;
          return validValue;
        });
      }
    }, TIMER_CONFIG.UPDATE_INTERVAL);

    // Skip WebSocket initialization if browser is not compatible
    if (!isBrowserCompatible) {
      return;
    }

    // First, ensure any existing connections are closed
    if ((window as any).assistantSocket) {
      try {
        console.log('PPCAchat: Closing existing global WebSocket before creating new one');
        (window as any).assistantSocket.close(1000, 'Creating new connection');
        (window as any).assistantSocket = null;
      } catch (err) {
        console.error('Error closing existing global WebSocket:', err);
      }
    }

    // Initialize the WebSocket connection with minimal delay
    // Using optimized delay settings for faster performance
    setTimeout(() => {
      // Check if component is unmounting before attempting to connect
      if (isUnmountingRef.current) {
        // Component is unmounting, skipping WebSocket connection
        return;
      }

      if (assistantChatRef.current) {
        // Starting new WebSocket connection for PPCA
        assistantChatRef.current.startConnection()
          .then(() => {
            // Check again if component is unmounting
            if (isUnmountingRef.current) {
              // Component unmounted during connection, closing WebSocket
              assistantChatRef.current?.endConversation();
              return;
            }
            // PPCA WebSocket connection started successfully
          })
          .catch((err: Error) => {
            // Check if component is unmounting before attempting to reconnect
            if (isUnmountingRef.current) {
              console.log('PPCAchat: Component is unmounting, skipping WebSocket reconnection');
              return;
            }

            console.error('PPCAchat: Failed to start PPCA WebSocket connection:', err);
            // Try one more time after a delay
            setTimeout(() => {
              // Check again if component is unmounting
              if (isUnmountingRef.current) {
                console.log('PPCAchat: Component is unmounting, skipping WebSocket reconnection');
                return;
              }

              console.log('PPCAchat: Attempting to reconnect PPCA WebSocket...');
              if (assistantChatRef.current) {
                assistantChatRef.current.startConnection()
                  .catch((retryErr: Error) => {
                    console.error('PPCAchat: Second attempt to connect PPCA WebSocket failed:', retryErr);
                  });
              }
            }, AGENT_ROLES.RETRY_DELAY); // Now using optimized delay from config
          });
      }
    }, AGENT_ROLES.CONNECTION_DELAY);

    return () => {
      // Set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;
      console.log('PPCAchat: Component is unmounting, setting unmounting flag');

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      // Clear the timer
      clearInterval(timer);

      // Clear any pending debounced time updates
      if (timeUpdateTimeoutRef.current) {
        clearTimeout(timeUpdateTimeoutRef.current);
        timeUpdateTimeoutRef.current = null;
      }

      // Use immediate time update for unmount since it's critical
      const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);
      debouncedTimeUpdate(remainingTimeInSeconds, true); // Use immediate update for unmount

      console.log('PPCAchat: Using backup remaining time for next login');

      // Clean up WebSocket connection when component unmounts
      try {
        if (assistantChatRef.current && typeof assistantChatRef.current.endConversation === 'function') {
          console.log('PPCAchat: Safely closing WebSocket connection on unmount');
          assistantChatRef.current.endConversation();
        }

        // Force close any remaining connections
        if (typeof window !== 'undefined') {
          // Clean up global socket if it exists
          if ((window as any).assistantSocket) {
            console.log('PPCAchat: Force closing any remaining WebSocket connections on unmount');
            try {
              (window as any).assistantSocket.close(1000, 'Component unmounting');
            } catch (e) {
              // Ignore errors during close
            }
            (window as any).assistantSocket = null;
          }
        }
      } catch (err) {
        console.log('PPCAchat: Error during WebSocket cleanup on unmount, continuing:', err);
      }
    };
  }, [handleFinish]); // Removed timeRemaining from deps to prevent re-running effect on timer changes

  // No need to calculate progress width anymore as it's handled by the ProgressBar component



  // Expose safe logout function globally for UserActionButtons
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).ppcaSafeLogout = handleSafeLogout;
    }

    // Cleanup on unmount
    return () => {
      if (typeof window !== 'undefined') {
        (window as any).ppcaSafeLogout = null;
      }
    };
  }, [handleSafeLogout]);

  // Simple time tracking for tab close detection
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Only set up event listeners if we have user data and token
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    const userId = user?.id;

    if (!token || !userId) {
      return;
    }

    // Consolidated beforeunload handler with fetch instead of beacon
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Send voice logs before unload
      try {
        import('../utils/voiceLogging').then(({ sendVoiceLogsBatch }) => {
          sendVoiceLogsBatch('beforeunload').catch(() => {
            // Can't log errors during page unload
          });
        }).catch(() => {
          // Can't log errors during page unload
        });
      } catch (err) {
        // Can't log errors during page unload
      }

      // Get current remaining time
      const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);

      // Use fetch with keepalive to our Next.js API route
      fetch('/api/users/me/time-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          remaining_time: remainingTimeInSeconds,
          userId: userId
        }),
        keepalive: true
      }).catch(err => {
        // Can't log errors during page unload, but we tried our best
      });

      // Show warning if user has significant time remaining
      const remainingTime = timeRemainingRef.current;
      if (remainingTime > 60) { // More than 1 minute remaining
        const message = lng === "de"
          ? "Sie haben noch Gesprächszeit übrig. Möchten Sie wirklich die Seite verlassen?"
          : "You still have conversation time remaining. Are you sure you want to leave?";

        event.preventDefault();
        event.returnValue = message;
        return message;
      }
    };

    // Handle pagehide with fetch instead of beacon
    const handlePageHide = () => {
      // Send voice logs before page hide
      try {
        import('../utils/voiceLogging').then(({ sendVoiceLogsBatch }) => {
          sendVoiceLogsBatch('pagehide').catch(() => {
            // Can't log errors during page hide
          });
        }).catch(() => {
          // Can't log errors during page hide
        });
      } catch (err) {
        // Can't log errors during page hide
      }

      const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);

      fetch('/api/users/me/time-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          remaining_time: remainingTimeInSeconds,
          userId: userId
        }),
        keepalive: true
      }).catch(err => {
        // Can't log errors during page hide, but we tried our best
      });
    };

    // Handle visibilitychange with fetch instead of beacon
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        const remainingTime = Math.floor(timeRemainingRef.current);
        
        fetch('/api/users/me/time-update', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ 
            remaining_time: remainingTime,
            userId: userId
          }),
          keepalive: true
        }).catch(err => {
          console.error('Error updating time on visibility change:', err);
        });
      }
    };

    // Add event listeners - using consolidated handlers to prevent conflicts
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handlePageHide); // Most reliable for mobile
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pagehide', handlePageHide);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, lng]); // Dependencies: user for ID, lng for warning message

  return (
    <div className="text-center max-w-md mx-auto">
      {/* Error messages are now handled by the global BrowserCompatibilityNotification component */}



      <div className="flex flex-col justify-center items-center">
         <div className="flex justify-center items-center mb-4 relative w-full">
          <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 lg:w-64 lg:h-64">
            {/* Avatar component */}
            <DynamicAvatar
              gender={avatarGender}
              isActive={isConnected}
              isSpeaking={isSpeaking}
              audioLevel={audioLevel}
              onLottieRef={handleLottieRef}
            />
          </div>
        </div>

        {/* Common Feedback Button - positioned just below avatar */}
        {/* <div className="mb-3">
          <button
            onClick={handleCommonFeedbackClick}
            disabled={isSubmitting || assistantState !== 'ready' || isFirstSentenceMuted}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              isSubmitting || assistantState !== 'ready' || isFirstSentenceMuted
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600 cursor-pointer'
            }`}
          >
            {t("common_feedback") || "Common Feedback"}
          </button>
        </div> */}

        {/* Assistant status message - positioned below button with proper spacing */}
        {assistantState !== 'ready' && (
          <div className="flex justify-center mb-3">
            <p className="text-base font-bold text-[#52bcc3] text-center whitespace-nowrap animate-pulse">
              {assistantState === 'getting_started'
                ? (t("luma_getting_started") || "Luma is getting started...")
                : (t("luma_speaking") || "Luma is speaking...")
              }
            </p>
          </div>
        )}

        {/* WebSocket controls component */}
        <WebSocketControls
          lng={lng}
          isRecording={isRecording && !isFirstSentenceMuted} // Show as not recording during first sentence
          isConnected={isConnected} // Allow reconnection even when assistant is not ready
          isPaused={isPaused}
          onMicClick={() => {
            // Prevent manual microphone control during first sentence
            if (isFirstSentenceMuted) {
              console.log('PPCAchat: Manual mic click blocked - waiting for assistant to finish first sentence');
              return;
            }

            // Use the reusable handler function from AssistantCore
            assistantHandlers.handleMicClick(
              assistantChatRef,
              isRecording,
              isUnmountingRef,
              'PPCAchat',
              (recording) => {
                setIsRecording(recording);
                // Update microphone enabled ref for timer
                isMicrophoneEnabledRef.current = recording;
                // Update assistant state if microphone becomes available
                if (recording && assistantState !== 'ready') {
                  setAssistantState('ready');
                }
                console.log(`PPCAchat: Manual mic click - microphone ${recording ? 'enabled' : 'disabled'}`);
              }
            );
          }}
          onStopClick={() => {
            // Use the reusable handler function from AssistantCore with a custom callback
            assistantHandlers.handleStopClick(
              assistantChatRef,
              isPaused,
              isUnmountingRef,
              'PPCAchat',
              (newPausedState) => {
                setIsPaused(newPausedState);
                // Also update the ref directly for immediate effect on the timer
                isPausedRef.current = newPausedState;
                // Updated isPausedRef in onStopClick
              },
              // Custom callback for PPCA-specific functionality
              () => {
                // Use debounced time update to prevent WebSocket interference
                const remainingTimeInSeconds = Math.floor(timeRemainingRef.current);
                debouncedTimeUpdate(remainingTimeInSeconds, false); // Use debounced update for pause/stop
              }
            );
          }}
          onReconnectClick={async () => {
            try {
              console.log('PPCAchat: Manual reconnect clicked');

              // Reset assistant state for fresh connection
              setAssistantState('getting_started');
              setIsRecording(false);
              setIsConnected(false);

              // Use the reusable handler function from AssistantCore
              await assistantHandlers.handleReconnectClick(
                assistantChatRef,
                isPaused,
                isUnmountingRef,
                'PPCAchat',
                (newPausedState) => {
                  setIsPaused(newPausedState);
                  // Also update the ref directly for immediate effect on the timer
                  isPausedRef.current = newPausedState;
                  console.log('PPCAchat: Reconnect completed, paused state:', newPausedState);
                }
              );

              console.log('PPCAchat: Reconnection process completed');
            } catch (err) {
              console.error('PPCAchat: Error during manual reconnect:', err);
            }
          }}

        />

        {/* AssistantChat component to handle WebSocket connection */}
        <div className="h-0 overflow-hidden">
          <AssistantChat
            ref={(ref) => {
              assistantChatRef.current = ref;
              // Check if the ref has the isPaused property and update state accordingly
              if (ref && 'isPaused' in ref) {
                const newPausedState = ref.isPaused;
                // Only update if the state has changed to avoid unnecessary renders
                if (isPaused !== newPausedState) {
                  console.log('PPCAchat: Syncing isPaused state with AssistantChat component:', newPausedState);
                  setIsPaused(newPausedState);
                  // Also update the ref directly for immediate effect on the timer
                  isPausedRef.current = newPausedState;
                }
              }
            }}
            userToken={localStorage.getItem(STORAGE_KEYS.TOKEN) || ''}
            avatarGender={avatarGender}
            agentRole={AgentRoleEnum.PPCA}
            language={user?.details?.language || lng}
            onConversationComplete={() => {
              // Conversation completed
            }}
            onRecordingStateChange={(recording) => {
              // Only update recording state if not in first sentence muting mode
              if (!isFirstSentenceMuted) {
                setIsRecording(recording);
                // Update microphone enabled ref for timer
                isMicrophoneEnabledRef.current = recording;

                // Track assistant state - if microphone becomes available, assistant has finished first message
                if (recording && assistantState !== 'ready') {
                  console.log('PPCAchat: Assistant finished first message - enabling UI controls');
                  setAssistantState('ready');
                }

                console.log(`PPCAchat: Microphone ${recording ? 'enabled' : 'disabled'} - timer will ${recording ? 'start' : 'pause'}`);
              } else {
                console.log('PPCAchat: Ignoring recording state change during first sentence muting');
              }
            }}
            onConnectionStateChange={(connected) => {
              setIsConnected(connected);
              // Also update the ref directly for immediate effect on the timer
              isConnectedRef.current = connected;
              console.log(`PPCAchat: Connection ${connected ? 'established' : 'lost'} - timer state: connected=${connected}, mic=${isMicrophoneEnabledRef.current}, paused=${isPausedRef.current}`);

              // If connection is established and we were paused (e.g., after Common Feedback), update states
              // But only if we're not in the middle of a manual restoration process
              if (connected && isPausedRef.current && !isRestoringFromCommonFeedback) {
                console.log('PPCAchat: Connection established after being paused - updating states');
                setIsPaused(false);
                isPausedRef.current = false;
                setAssistantState('ready');
                // Don't reset first sentence muting if we're resuming
              }
              // If connection is lost, reset all states including first sentence muting
              else if (!connected) {
                setIsPaused(false);
                isPausedRef.current = false;
                // Also reset microphone state when connection is lost
                isMicrophoneEnabledRef.current = false;
                setAssistantState('getting_started');
                setIsFirstSentenceMuted(true); // Reset first sentence muting for new connection
                console.log('PPCAchat: Connection lost - resetting all states including first sentence muting');
              }
              // Note: We don't set 'speaking' here anymore - we'll detect it when audio actually starts
            }}
          />
        </div>
      </div>

      <div className="-mt-1">
        {/* Progress bar timer instead of button */}
        <div className="flex flex-col items-center">
          <div className="w-full max-w-xs mb-1">
            <div className="h-4 bg-white border border-gray-300 rounded-full overflow-hidden shadow-inner">
              <div
                className="h-full bg-gradient-to-r from-[#3da8af] to-[#52bcc3] transition-all duration-1000 ease-linear"
                style={{ width: `${(timeRemaining / totalTime) * 100}%` }}
              ></div>
            </div>
          </div>
          <div className="text-sm text-gray-600 mb-1">
            {t("remaining_conversation_time") || "Remaining conversation time:"} <span className="font-medium">{Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}</span>
          </div>
          <button
            onClick={handleCommonFeedbackClick}
            disabled={isSubmitting || assistantState !== 'ready' || isFirstSentenceMuted}
            className={`text-sm transition-colors ${
              isSubmitting || assistantState !== 'ready' || isFirstSentenceMuted
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-[#52bcc3] hover:text-[#3da8af]'
            }`}
          >
            {t("give_feedback") || "Give feedback"}
          </button>
        </div>
      </div>

      {/* Common Feedback Modal */}
      <CommonFeedbackModal
        lng={lng}
        avatarGender={avatarGender}
        isOpen={isCommonFeedbackModalOpen}
        onClose={handleCommonFeedbackModalClose}
        onSubmitComplete={handleCommonFeedbackComplete}
      />
    </div>
  );
}
