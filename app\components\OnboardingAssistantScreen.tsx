"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { getTranslation } from "../i18n/translations";
import { signupService } from "../services/signupService";
import { setUserStage, checkAssistantStatus } from "../services/api";
import { useAuth } from "../context/ApiAuthContext";
import AssistantChat, { assistantHandlers } from "./AssistantCore";
import DynamicAvatar from "./AssistantAvatar";
import { AgentRoleEnum } from "../types/enums";
import { STORAGE_KEYS } from "../config/appConfig";
import WebSocketControls from "./AssistantControls";
import * as audioUtils from '../utils/audioStreamingStatic';
import { requestWakeLock, releaseWakeLock } from '../utils/wakeLock';

// Component for conversation functionality after avatar selection
export default function ConversationPage({
  lng,
  avatarGender,
  onComplete
}: {
  lng: string,
  avatarGender: "male" | "female",
  onComplete?: () => void
}) {
  const { user } = useAuth();
  const [error, setError] = useState("");
  const [buttonText, setButtonText] = useState<string>("start_conversation");
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const [lottieInstance, setLottieInstance] = useState<any>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);

  // Loading screen states
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingProgress, setLoadingProgress] = useState<number>(0);
  const [showCreateButton, setShowCreateButton] = useState<boolean>(false);
  const assistantChatRef = useRef<any>(null);
  const isUnmountingRef = useRef<boolean>(false); // Ref to track if component is unmounting

  // Use the t function from our static translations
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Get user's first name
  const [firstName, setFirstName] = useState<string>("User");

  useEffect(() => {
    // Simple first name extraction
    if (user && 'first_name' in user) {
      setFirstName(user.first_name as string);
    } else if (user?.details && 'first_name' in user.details) {
      setFirstName(user.details.first_name as string);
    } else {
      const userDetails = signupService.getUserDetails();
      if (userDetails?.first_name) {
        setFirstName(userDetails.first_name);
      }
    }
  }, [user]);

  // Dynamic loading screen effect - triggered when Create my assistant is clicked
  const startLoadingScreen = useCallback(() => {
    let progressInterval: NodeJS.Timeout;
    let statusCheckInterval: NodeJS.Timeout;
    let currentProgress = 0;

    // Check status after OBA conversation
    const checkStatus = async () => {
      try {
        const response = await checkAssistantStatus();

        if (response.status === 'in_progress') {
          // Show loading spinner and continue polling
          console.log('Assistant creation in progress, continuing to poll...');
          // Slowly increment progress while in progress
          if (currentProgress < 90) {
            currentProgress += Math.random() * 1.5; // Slow random increment
            setLoadingProgress(Math.min(currentProgress, 90));
          }
          // Poll again in a few seconds
          statusCheckInterval = setTimeout(checkStatus, 3000);
        } else if (response.status === 'complete') {
          // Complete the progress bar quickly and proceed
          console.log('Assistant creation complete, finishing loading...');

          // Clear any existing intervals
          if (progressInterval) clearInterval(progressInterval);
          if (statusCheckInterval) clearTimeout(statusCheckInterval);

          // Quickly complete the progress bar
          const completeProgress = () => {
            currentProgress += 5;
            setLoadingProgress(Math.min(currentProgress, 100));

            if (currentProgress >= 100) {
              setTimeout(async () => {
                setIsLoading(false);

                // Cleanup was already done immediately when button was clicked
                console.log('ConversationPage: Loading complete, proceeding to navigation (cleanup already done)');

                // Send voice logs before completing onboarding
                try {
                  import('../utils/voiceLogging').then(({ endVoiceLoggingSession }) => {
                    endVoiceLoggingSession('onboarding_complete').catch(err => {
                      console.log('Error ending voice logging session in OnboardingAssistant:', err);
                    });
                  }).catch(err => {
                    console.log('Error importing voice logging during onboarding completion:', err);
                  });
                } catch (err) {
                  console.log('Error with voice logging during onboarding completion:', err);
                }

                // Set the unmounting flag to prevent new connections
                isUnmountingRef.current = true;
                console.log('ConversationPage: Setting unmounting flag before navigation');

                try {
                  // Update the user's stage to 4 in the backend
                  await setUserStage(4);

                  // If onComplete callback is provided, use it to move to the next screen
                  if (onComplete) {
                    onComplete();
                  } else {
                    // Fallback to dashboard navigation - we should never reach this since onComplete is always provided
                    window.location.href = `/${lng}/dashboard`;
                  }
                } catch (error) {
                  console.error('Failed to update stage:', error);
                  setError(t("error_update_failed") || "Failed to update. Please try again.");
                  setIsLoading(false);
                }
              }, 300); // Small delay to show 100% before hiding
            } else {
              setTimeout(completeProgress, 50);
            }
          };

          completeProgress();
        } else {
          // Handle 'not_started' or other statuses - treat as complete for now
          console.log('Assistant status:', response.status, '- treating as complete');
          statusCheckInterval = setTimeout(checkStatus, 3000);
        }
      } catch (error) {
        console.error('Error checking assistant status:', error);
        // On error, fall back to time-based completion after a delay
        setTimeout(async () => {
          setLoadingProgress(100);
          setTimeout(async () => {
            setIsLoading(false);
            isUnmountingRef.current = true;

            try {
              await setUserStage(4);
              if (onComplete) {
                onComplete();
              } else {
                window.location.href = `/${lng}/dashboard`;
              }
            } catch (stageError) {
              console.error('Failed to update stage:', stageError);
              setError(t("error_update_failed") || "Failed to update. Please try again.");
              setIsLoading(false);
            }
          }, 300);
        }, 2000);
      }
    };

    // Start initial progress animation
    progressInterval = setInterval(() => {
      if (currentProgress < 60) {
        currentProgress += Math.random() * 1; // Very slow initial progress
        setLoadingProgress(Math.min(currentProgress, 60));
      }
    }, 100);

    // Start status checking after 1-2 seconds delay
    setTimeout(checkStatus, 2000);

    return () => {
      if (progressInterval) clearInterval(progressInterval);
      if (statusCheckInterval) clearTimeout(statusCheckInterval);
    };
  }, [lng, onComplete, t]);

  // Handle continue button click
  const handleContinue = useCallback(async () => {
    try {
      // If this is the first click (Start Conversation button)
      if (buttonText === "start_conversation") {
        // Update UI to show the conversation screen
        setButtonText("create_assistant");
        // Hide the create assistant button initially
        setShowCreateButton(false);

        // Set a timeout to show the create assistant button after delay
        setTimeout(() => {
          // Check if component is still mounted before updating state
          if (!isUnmountingRef.current) {
            console.log('ConversationPage: Showing Create Assistant button after delay');
            setShowCreateButton(true);
          }
        }, 1000);

        // Initialize the WebSocket connection
        setTimeout(() => {
          // Check if component is unmounting
          if (isUnmountingRef.current) {
            console.log('ConversationPage: Component is unmounting, skipping WebSocket connection');
            return;
          }

          if (assistantChatRef.current) {
            console.log('ConversationPage: Starting WebSocket connection after Start Conversation click');
            assistantChatRef.current.startConnection()
              .then(() => {
                console.log('ConversationPage: WebSocket connection started successfully');
              })
              .catch((err: Error) => {
                console.error('ConversationPage: Failed to start WebSocket connection:', err);
              });
          }
        }, 100);
      }
      // If this is the second click (Create my assistant button)
      else if (buttonText === "create_assistant") {
        // Set loading state immediately to prevent OBA screen flash
        console.log('ConversationPage: Setting loading state immediately');
        setIsLoading(true);
        setLoadingProgress(0);

        // Stop all audio and close WebSocket immediately when button is clicked
        console.log('ConversationPage: Stopping audio and closing WebSocket immediately on button click');

        // Use the emergency cleanup function - this handles everything:
        // - Sets unmounting flag
        // - Stops all audio playback
        // - Clears audio queue
        // - Closes WebSocket connections
        // - Stops microphone and releases permissions
        // - Closes audio context
        try {
          audioUtils.emergencyLogoutCleanup();
          console.log('Emergency cleanup completed immediately - all audio, microphone, and WebSocket stopped');
        } catch (err) {
          console.error('Error during immediate emergency cleanup:', err);
        }

        // Start the dynamic loading screen progress - cleanup is already done
        console.log('ConversationPage: Starting dynamic loading screen, cleanup already completed');
        startLoadingScreen();
      }
    } catch (error) {
      console.error('Failed to update stage:', error);
      setError(t("error_update_failed") || "Failed to update. Please try again.");
    }
  }, [buttonText, lng, onComplete, t, startLoadingScreen]);

  // No need to calculate progress width anymore as it's handled by the ProgressBar component

  // Effect to handle audio level simulation when recording
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRecording) {
      // Simulate audio levels when recording
      interval = setInterval(() => {
        // Generate random audio level between 0.3 and 0.8
        const randomLevel = 0.3 + Math.random() * 0.5;
        setAudioLevel(randomLevel);
      }, 200);
    } else {
      // Reset audio level when not recording
      setAudioLevel(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  // Handle Lottie reference
  const handleLottieRef = useCallback((instance: any) => {
    setLottieInstance(instance);
    // Lottie instance received
  }, []);

  // Monitor assistant speaking state to update avatar
  useEffect(() => {
    if (!isConnected) {
      setIsSpeaking(false);
      return;
    }

    const monitorAssistantSpeaking = async () => {
      try {
        const audioUtils = await import('../utils/audioStreamingStatic');
        const isCurrentlySpeaking = audioUtils.getAssistantSpeakingState();
        setIsSpeaking(isCurrentlySpeaking);
      } catch (err) {
        console.error('Error monitoring assistant speaking state:', err);
      }
    };

    // Poll for speaking state changes
    const interval = setInterval(monitorAssistantSpeaking, 3000);

    // Initial check
    monitorAssistantSpeaking();

    return () => clearInterval(interval);
  }, [isConnected]);

  // Effect to update connection status
  useEffect(() => {
    if (buttonText !== "create_assistant") return;

    const updateConnectionStatus = () => {
      const indicator = document.getElementById('connection-indicator');
      const status = document.getElementById('connection-status');

      if (!indicator || !status) return;

      // Check if window.assistantSocket exists and get its state
      const socket = (window as any).assistantSocket;

      if (socket && socket.readyState === WebSocket.OPEN) {
        // Connected
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-green-500';
        indicator.innerHTML = '';
        status.textContent = t("connected") || "Connected";
        status.className = 'font-medium text-green-700';
        setIsConnected(true);
      } else if (socket && socket.readyState === WebSocket.CONNECTING) {
        // Connecting
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-yellow-500 relative';
        indicator.innerHTML = '<span class="absolute inset-0 rounded-full bg-yellow-500 animate-ping opacity-75"></span>';
        status.textContent = t("connecting") || "Connecting...";
        status.className = 'font-medium text-yellow-700';
        setIsConnected(false);
      } else {
        // Disconnected or error
        indicator.className = 'inline-block w-3 h-3 rounded-full mr-2 bg-red-500';
        indicator.innerHTML = '';
        status.textContent = t("disconnected_click_reconnect") || "Disconnected (click to reconnect)";
        status.className = 'font-medium text-red-700';
        setIsConnected(false);
      }
    };

    // Update immediately and periodically
    updateConnectionStatus();
    const interval = setInterval(updateConnectionStatus, 5000);

    // Update when window gets focus
    window.addEventListener('focus', updateConnectionStatus);

    return () => {
      // Set the unmounting flag to prevent new connections
      isUnmountingRef.current = true;
      console.log('ConversationPage: Component is unmounting, setting unmounting flag');

      // Send voice logs before cleanup
      try {
        import('../utils/voiceLogging').then(({ sendVoiceLogsBatch }) => {
          sendVoiceLogsBatch('component_unmount').catch(err => {
            console.log('Error sending voice logs during OnboardingAssistant unmount:', err);
          });
        }).catch(err => {
          console.log('Error importing voice logging during OnboardingAssistant unmount:', err);
        });
      } catch (err) {
        console.log('Error with voice logging during OnboardingAssistant unmount:', err);
      }

      // Set the unmounting flag in audioUtils to prevent new operations
      audioUtils.setUnmountingFlag(true);

      clearInterval(interval);
      window.removeEventListener('focus', updateConnectionStatus);

      // Clean up WebSocket connection when component unmounts or when buttonText changes
      if (assistantChatRef.current) {
        console.log('ConversationPage: Closing WebSocket connection on unmount');
        assistantChatRef.current.endConversation();
      }

      // Also close any global socket that might be lingering
      if ((window as any).assistantSocket) {
        try {
          // Closing global WebSocket on unmount
          (window as any).assistantSocket.close(1000, 'Component unmounted');
          (window as any).assistantSocket = null;
        } catch (err) {
          console.error('Error closing global WebSocket:', err);
        }
      }
    };
  }, [buttonText]);

  // Wake lock effect - prevent screen auto-lock on mobile/tablet devices
  useEffect(() => {
    let wakeLockRequested = false;

    const handleWakeLock = async () => {
      // Only request wake lock when the assistant is active (conversation screen) and not paused
      if (buttonText === "create_assistant" && isConnected && !isPaused) {
        if (!wakeLockRequested) {
          const success = await requestWakeLock('OnboardingAssistant');
          if (success) {
            wakeLockRequested = true;
            console.log('OnboardingAssistant: Wake lock acquired to prevent screen auto-lock');
          }
        }
      } else {
        // Release wake lock when not in conversation, disconnected, or paused
        if (wakeLockRequested) {
          await releaseWakeLock('OnboardingAssistant');
          wakeLockRequested = false;
          console.log('OnboardingAssistant: Wake lock released');
        }
      }
    };

    handleWakeLock();

    // Cleanup function to release wake lock when component unmounts
    return () => {
      if (wakeLockRequested) {
        releaseWakeLock('OnboardingAssistant');
        console.log('OnboardingAssistant: Wake lock released on component unmount');
      }
    };
  }, [buttonText, isConnected, isPaused]);

  // Show loading screen when transitioning to conversation
  if (isLoading) {
    return (
      <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0 flex flex-col justify-center items-center min-h-[400px]">
        {/* Loading Avatar - larger for conversation screen */}
        <div className="flex justify-center items-center mb-6 relative w-full">
          <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 lg:w-64 lg:h-64">
            <DynamicAvatar
              gender={avatarGender}
              isActive={false}
              audioLevel={0}
              onLottieRef={handleLottieRef}
            />
          </div>
        </div>

        {/* Loading Text */}
        <div className="mb-6 text-lg sm:text-xl font-medium text-gray-800">
          {lng === "de" ? "Erstelle deinen Assistenten..." : "Creating your assistant..."}
        </div>

        {/* Progress Bar */}
        <div className="w-full max-w-sm mb-4">
          <div className="bg-gray-300 rounded-full h-3 overflow-hidden shadow-inner border border-gray-200">
            <div
              className="bg-gradient-to-r from-[#52bcc3] to-[#3da8af] h-full rounded-full transition-all duration-300 ease-out shadow-sm"
              style={{ width: `${loadingProgress}%` }}
            />
          </div>
        </div>

        {/* Progress Percentage */}
        <div className="text-base text-gray-700 font-semibold">
          {Math.round(loadingProgress)}%
        </div>
      </div>
    );
  }

  return (
    <div className="text-center max-w-xs sm:max-w-sm md:max-w-md mx-auto px-2 sm:px-0">
      {error && (
        <div className="p-2 sm:p-3 mb-3 sm:mb-4 mx-2 sm:mx-4 bg-red-500/10 border border-red-500/30 text-red-500 rounded-md text-xs sm:text-sm">
          {error}
        </div>
      )}

      <div className="flex flex-col justify-center items-center">
        {buttonText === "create_assistant" ? (
          <>
            <div className="flex justify-center items-center mb-3 sm:mb-4 relative w-full">
              <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48 md:w-56 md:h-56 lg:w-64 lg:h-64">
                <DynamicAvatar
                  gender={avatarGender}
                  isActive={isConnected}
                  isSpeaking={isSpeaking}
                  audioLevel={audioLevel}
                  onLottieRef={handleLottieRef}
                />
              </div>
            </div>

            {/* WebSocket controls component */}
            <WebSocketControls
              lng={lng}
              isRecording={isRecording}
              isConnected={isConnected}
              isPaused={isPaused}
              onMicClick={() => {
                // Use the reusable handler function from AssistantCore
                assistantHandlers.handleMicClick(
                  assistantChatRef,
                  isRecording,
                  isUnmountingRef,
                  'ConversationPage',
                  setIsRecording
                );
              }}
              onStopClick={() => {
                // Use the reusable handler function from AssistantCore
                assistantHandlers.handleStopClick(
                  assistantChatRef,
                  isPaused,
                  isUnmountingRef,
                  'ConversationPage',
                  setIsPaused
                );
              }}
              onReconnectClick={() => {
                // Use the reusable handler function from AssistantCore
                assistantHandlers.handleReconnectClick(
                  assistantChatRef,
                  isPaused,
                  isUnmountingRef,
                  'ConversationPage',
                  setIsPaused
                );
              }}
            />

            {/* AssistantChat component to handle WebSocket connection */}
            <div className="h-0 overflow-hidden">
              <AssistantChat
                ref={(ref) => {
                  assistantChatRef.current = ref;
                  // Check if the ref has the isPaused property and update state accordingly
                  if (ref && 'isPaused' in ref) {
                    setIsPaused(ref.isPaused);
                  }
                }}
                userToken={localStorage.getItem(STORAGE_KEYS.TOKEN) || ''}
                avatarGender={avatarGender}
                agentRole={AgentRoleEnum.OBA}
                language={lng}
                onConversationComplete={() => {
                  // Conversation completed
                }}
                onRecordingStateChange={(recording) => {
                  setIsRecording(recording);
                }}
                onConnectionStateChange={(connected) => {
                  setIsConnected(connected);

                  // If connection is lost, reset paused state
                  if (!connected) {
                    setIsPaused(false);
                  }
                }}
              />
            </div>
          </>
        ) : (
          <>
            {/* Welcome text for second screen */}
            <div className="mb-4 sm:mb-6 text-base sm:text-lg font-medium text-gray-800 bg-gradient-to-r from-[#52bcc3] to-[#3da8af] bg-clip-text text-transparent">
              {t("hi_user").replace("{name}", firstName)}<br />
              {lng === "de" ? (
                <>
                  Klicken Sie auf <span className="font-bold">Gespräch beginnen</span>, um die Personalisierung von Luma zu vervollständigen.
                </>
              ) : (
                <>
                  Click <span className="font-bold">Start conversation</span> to begin the personalisation of your assistant.
                </>
              )}
            </div>

            <div className="flex justify-center items-center mb-3 sm:mb-4 relative w-full">
              <div className="relative mx-auto w-32 h-32 sm:w-36 sm:h-36 md:w-40 md:h-40 lg:w-44 lg:h-44">
                <DynamicAvatar
                  gender={avatarGender}
                  isActive={false}
                  audioLevel={0}
                  onLottieRef={handleLottieRef}
                />
              </div>
            </div>

            {/* Avatar type label with enhanced styling */}
            <div className="mb-2 text-sm sm:text-base font-medium text-gray-800 bg-[#52bcc3]/10 py-1 sm:py-1.5 px-3 sm:px-4 rounded-full inline-block">
              {avatarGender === "female" ? t("female_avatar") || "Female Avatar" : t("male_avatar") || "Male Avatar"}
            </div>


          </>
        )}
      </div>

      <div className="mt-4 sm:mt-6">
        {/* Only show the button if it's "Start Conversation" or if it's "Create my assistant" and showCreateButton is true */}
        {(buttonText === "start_conversation" || (buttonText === "create_assistant" && showCreateButton)) && (
          <button
            onClick={handleContinue}
            disabled={buttonText === "start_conversation" && isRecording}
            className={`min-w-[100px] sm:min-w-[120px] py-2 sm:py-2.5 px-4 sm:px-6 ${
              buttonText === "start_conversation" && isRecording
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer"
            } text-white text-sm sm:text-base rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 whitespace-nowrap`}
          >
            {buttonText === "create_assistant" && (t("create_assistant") || "Create my assistant")}
            {buttonText === "start_conversation" && (<span className="font-bold">{t("start_conversation") || "Start Conversation"}</span>)}
          </button>
        )}

        {/* Show loading message with spinner when button is hidden */}
        {buttonText === "create_assistant" && !showCreateButton && (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 border-2 border-t-[#52bcc3] border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
            <div className="text-sm text-gray-600">
              Preparing your assistant...
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
