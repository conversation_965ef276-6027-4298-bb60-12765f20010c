"use client";

import { useState, useRef, forwardRef, useImperativeHandle } from "react";
import { useAuth } from "../context/ApiAuthContext";
import { useBrowserCompatibility } from "../context/BrowserCompatibilityContext";
import { AgentRoleEnum } from "../types/enums";
import { WS_CONFIG, AGENT_ROLES, ERROR_MESSAGES, AUDIO_CONFIG } from '../config/appConfig';
import * as audioUtils from '../utils/audioStreamingStatic';

interface CommonFeedbackAssistantProps {
  userToken: string;
  avatarGender: 'male' | 'female';
  language?: string;
  onConversationComplete?: () => void;
  onRecordingStateChange?: (isRecording: boolean) => void;
  onConnectionStateChange?: (isConnected: boolean) => void;
}

export interface CommonFeedbackAssistantHandle {
  startConnection: () => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  endConversation: () => void;
  assistantSocket: WebSocket | null;
}

const CommonFeedbackAssistant = forwardRef<CommonFeedbackAssistantHandle, CommonFeedbackAssistantProps>(
  ({ userToken, avatarGender, language = 'en', onConversationComplete, onRecordingStateChange, onConnectionStateChange }, ref) => {
    const { user } = useAuth();
    const { isBrowserCompatible } = useBrowserCompatibility();

    const [error, setError] = useState<string | null>(null);
    const socketRef = useRef<WebSocket | null>(null);
    const reconnectAttemptsRef = useRef<number>(0);
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [isConnected, setIsConnected] = useState<boolean>(false);
    const isIntentionallyClosingRef = useRef<boolean>(false);

    // Connect to WebSocket with retry logic
    const connectWebSocket = async () => {
      if (!isBrowserCompatible) {
        console.error('CommonFeedbackAssistant: Browser is not compatible with required features');
        setError('Your browser does not support all required features. Some functionality may be limited.');
        setIsConnected(false);
        if (onConnectionStateChange) onConnectionStateChange(false);
        return;
      }

      // Clear any existing reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      try {
        const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL || 'wss://api.example.com'}/ws`;
        const socket = new WebSocket(wsUrl);

        socket.onopen = async () => {
          console.log('CommonFeedbackAssistant: WebSocket connection established');
          
          // DO NOT set global window.assistantSocket - keep this isolated
          console.log('CommonFeedbackAssistant: Using isolated WebSocket instance');
          
          reconnectAttemptsRef.current = 0;
          setIsConnected(true);
          if (onConnectionStateChange) onConnectionStateChange(true);
          setError(null);

          // Initialize voice logging session
          try {
            const { initVoiceLoggingSession } = await import('../utils/voiceLogging');
            const sessionId = `${AgentRoleEnum.CM_FA}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            initVoiceLoggingSession(sessionId, user?.id?.toString(), AgentRoleEnum.CM_FA);
            console.log('CommonFeedbackAssistant: Voice logging session initialized:', sessionId);
          } catch (err) {
            console.log('CommonFeedbackAssistant: Error initializing voice logging session:', err);
          }

          socketRef.current = socket;

          // DO NOT set WebSocket in audioUtils to avoid conflicts with PPCA
          // CommonFeedbackAssistant will use its own isolated audio handling
          console.log('CommonFeedbackAssistant: Keeping audio pipeline isolated from PPCA');

          // Add delay to ensure WebSocket is fully ready
          await new Promise(resolve => setTimeout(resolve, 300));

          // Check if socket is still open before sending configuration
          if (socket.readyState === WebSocket.OPEN) {
            // Send session configuration
            const sessionConfig = {
              type: 'session.update',
              session: {
                modalities: ['text', 'audio'],
                instructions: 'You are a helpful assistant for collecting user feedback. Be concise and helpful.',
                voice: avatarGender === 'female' ? 'alloy' : 'echo',
                input_audio_format: 'pcm16',
                output_audio_format: 'pcm16',
                input_audio_transcription: {
                  model: 'whisper-1'
                },
                turn_detection: {
                  type: 'server_vad',
                  threshold: 0.5,
                  prefix_padding_ms: 300,
                  silence_duration_ms: 500
                },
                tools: [],
                tool_choice: 'auto',
                temperature: 0.8,
                max_response_output_tokens: 4096
              }
            };

            socket.send(JSON.stringify(sessionConfig));
            console.log('CommonFeedbackAssistant: Session configuration sent');
          } else {
            console.warn('CommonFeedbackAssistant: WebSocket not open, skipping session configuration');
          }

          await new Promise(resolve => setTimeout(resolve, 200));
        };

        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('CommonFeedbackAssistant: Received message:', data.type);

            switch (data.type) {
              case 'session.created':
                console.log('CommonFeedbackAssistant: Session created successfully');
                break;

              case 'session.updated':
                console.log('CommonFeedbackAssistant: Session updated successfully');
                break;

              case 'response.audio.delta':
                // Skip audio playback to avoid conflicts with PPCA
                console.log('CommonFeedbackAssistant: Received audio delta (skipping playback)');
                break;

              case 'response.audio_transcript.delta':
                console.log('CommonFeedbackAssistant: Received transcript delta');
                break;

              case 'input_audio_buffer.speech_started':
                console.log('CommonFeedbackAssistant: User started speaking');
                break;

              case 'conversation.item.input_audio_transcription.completed':
                if (data.transcript) {
                  console.log('CommonFeedbackAssistant: User transcript completed');
                }
                break;

              case 'response.done':
                console.log('CommonFeedbackAssistant: Response completed');
                break;

              case 'error':
                console.error('CommonFeedbackAssistant: WebSocket error:', data);
                setError(data.error?.message || 'An error occurred');
                break;

              default:
                console.log('CommonFeedbackAssistant: Unhandled message type:', data.type);
            }
          } catch (err) {
            console.error('CommonFeedbackAssistant: Error parsing WebSocket message:', err);
          }
        };

        socket.onerror = (error) => {
          console.error('CommonFeedbackAssistant: WebSocket error:', error);
          setError('Connection error occurred');
        };

        socket.onclose = (event) => {
          console.log('CommonFeedbackAssistant: WebSocket closed:', event.code, event.reason);
          setIsConnected(false);
          if (onConnectionStateChange) onConnectionStateChange(false);
          socketRef.current = null;

          // Only attempt reconnection if not intentionally closing
          if (!isIntentionallyClosingRef.current && reconnectAttemptsRef.current < WS_CONFIG.RECONNECT_ATTEMPTS) {
            reconnectAttemptsRef.current++;
            console.log(`CommonFeedbackAssistant: Attempting reconnection ${reconnectAttemptsRef.current}/${WS_CONFIG.RECONNECT_ATTEMPTS}`);
            
            reconnectTimeoutRef.current = setTimeout(() => {
              connectWebSocket();
            }, WS_CONFIG.RECONNECT_DELAY);
          } else if (reconnectAttemptsRef.current >= WS_CONFIG.RECONNECT_ATTEMPTS) {
            setError(ERROR_MESSAGES.CONNECTION.MAX_ATTEMPTS);
          }
        };

        socketRef.current = socket;
      } catch (err) {
        console.error('CommonFeedbackAssistant: Error creating WebSocket:', err);
        setError('Failed to create connection');
        setIsConnected(false);
        if (onConnectionStateChange) onConnectionStateChange(false);
      }
    };

    useImperativeHandle(ref, () => ({
      startConnection: async () => {
        console.log('CommonFeedbackAssistant: Starting connection');
        
        return audioUtils.safeWebSocketOperation(async () => {
          try {
            await audioUtils.initAudioContext();
            console.log('CommonFeedbackAssistant: Audio context initialized');
          } catch (audioErr) {
            console.error('CommonFeedbackAssistant: Error initializing audio context:', audioErr);
          }

          await connectWebSocket();
          await new Promise(resolve => setTimeout(resolve, AGENT_ROLES.CONNECTION_DELAY));
        }, 'CommonFeedbackAssistant', 'startConnection');
      },

      startRecording: async () => {
        console.log('CommonFeedbackAssistant: Start recording requested');

        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
          console.log('CommonFeedbackAssistant: WebSocket not connected, attempting to connect...');
          await connectWebSocket();

          let attempts = 0;
          while ((!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) && attempts < 5) {
            await new Promise(resolve => setTimeout(resolve, 200));
            attempts++;
          }
        }

        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
          throw new Error('CommonFeedbackAssistant: WebSocket is not connected');
        }

        // For now, just simulate recording without interfering with PPCA audio pipeline
        console.log('CommonFeedbackAssistant: Recording simulation started (avoiding audio conflicts)');
        if (onRecordingStateChange) onRecordingStateChange(true);
      },

      stopRecording: () => {
        console.log('CommonFeedbackAssistant: Stopping recording simulation');
        if (onRecordingStateChange) onRecordingStateChange(false);
      },

      endConversation: () => {
        console.log('CommonFeedbackAssistant: Ending conversation');
        isIntentionallyClosingRef.current = true;

        try {
          if (socketRef.current) {
            if (socketRef.current.readyState === WebSocket.OPEN) {
              socketRef.current.close(1000, 'Conversation ended');
            }
            socketRef.current = null;
          }

          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
          }

          setIsConnected(false);
          if (onConnectionStateChange) onConnectionStateChange(false);

          console.log('CommonFeedbackAssistant: Conversation ended successfully');
        } catch (err) {
          console.error('CommonFeedbackAssistant: Error ending conversation:', err);
        }
      },

      get assistantSocket() {
        return socketRef.current;
      },
    }));

    return <div className="hidden">Common Feedback Assistant Component</div>;
  }
);

CommonFeedbackAssistant.displayName = 'CommonFeedbackAssistant';

export default CommonFeedbackAssistant;
