"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { getTranslation } from "../i18n/translations";
import { useAuth } from "../context/ApiAuthContext";
import { useBrowserCompatibility } from "../context/BrowserCompatibilityContext";
import AssistantChat, { assistantHand<PERSON> } from "./AssistantCore";
import DynamicAvatar from "./AssistantAvatar";
import { AgentRoleEnum } from "../types/enums";
import { STORAGE_KEYS, AGENT_ROLES } from "../config/appConfig";
import WebSocketControls from "./AssistantControls";
import { requestWakeLock, releaseWakeLock } from '../utils/wakeLock';

interface CommonFeedbackModalProps {
  lng: string;
  avatarGender: "male" | "female";
  isOpen: boolean;
  onClose: () => void;
  onSubmitComplete?: () => void;
}

export default function CommonFeedbackModal({
  lng,
  avatarGender,
  isOpen,
  onClose,
  onSubmitComplete
}: CommonFeedbackModalProps) {
  // Get user data for language preference
  const { user } = useAuth();
  // Use browser compatibility context
  const { isBrowserCompatible } = useBrowserCompatibility();
  
  // Component state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [audioLevel] = useState<number>(0);
  const [isSpeaking] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for component lifecycle management
  const assistantChatRef = useRef<any>(null);
  const isUnmountingRef = useRef<boolean>(false);
  const wakeLockRef = useRef<any>(null);

  // Translation function
  const t = useCallback((key: string) => getTranslation(key, lng), [lng]);

  // Initialize wake lock when modal opens
  useEffect(() => {
    if (isOpen) {
      requestWakeLock().then(wakeLock => {
        wakeLockRef.current = wakeLock;
      }).catch(err => {
        console.log('Wake lock not supported or failed:', err);
      });
    }

    return () => {
      if (wakeLockRef.current) {
        releaseWakeLock(wakeLockRef.current);
        wakeLockRef.current = null;
      }
    };
  }, [isOpen]);

  // Initialize WebSocket connection when modal opens
  useEffect(() => {
    if (!isOpen || !isBrowserCompatible) {
      return;
    }

    // Reset unmounting flag
    isUnmountingRef.current = false;

    // Store the current PPCA socket before starting Common Feedback
    const ppcaSocket = (window as any).assistantSocket;
    console.log('CommonFeedbackModal: Storing PPCA socket for later restoration');

    // Initialize WebSocket connection with delay
    const initConnection = setTimeout(() => {
      if (isUnmountingRef.current || !isOpen) {
        return;
      }

      if (assistantChatRef.current) {
        console.log('CommonFeedbackModal: Starting WebSocket connection for Common Feedback');
        assistantChatRef.current.startConnection()
          .then(() => {
            if (isUnmountingRef.current || !isOpen) {
              assistantChatRef.current?.endConversation();
              return;
            }
            console.log('CommonFeedbackModal: WebSocket connection started successfully');
          })
          .catch((err: Error) => {
            if (isUnmountingRef.current || !isOpen) {
              return;
            }
            console.error('CommonFeedbackModal: Failed to start WebSocket connection:', err);
            setError('Failed to connect to feedback assistant');
          });
      }
    }, AGENT_ROLES.CONNECTION_DELAY);

    return () => {
      clearTimeout(initConnection);
      // Restore PPCA socket when modal closes
      if (ppcaSocket) {
        (window as any).assistantSocket = ppcaSocket;
        console.log('CommonFeedbackModal: Restored PPCA socket on cleanup');
      }
    };
  }, [isOpen, isBrowserCompatible]);

  // Cleanup when modal closes
  useEffect(() => {
    if (!isOpen) {
      // Set unmounting flag for this component only
      isUnmountingRef.current = true;

      console.log('CommonFeedbackModal: Starting cleanup - modal closed');

      // Stop any recording and clear audio from Common Feedback
      if (isRecording) {
        console.log('CommonFeedbackModal: Stopping recording');
        setIsRecording(false);
      }

      // Close Common Feedback WebSocket connection only
      if (assistantChatRef.current) {
        console.log('CommonFeedbackModal: Closing Common Feedback WebSocket connection');

        // End this conversation (this will close only the Common Feedback socket)
        assistantChatRef.current.endConversation();
      }

      // Reset states
      setIsRecording(false);
      setIsConnected(false);
      setIsPaused(false);
      setError(null);

      console.log('CommonFeedbackModal: Cleanup completed');

      // Reset unmounting flag for this component after cleanup
      setTimeout(() => {
        isUnmountingRef.current = false;
      }, 100);
    }
  }, [isOpen, isRecording]);

  // Handle modal close (cancel/close button - no feedback submission)
  const handleClose = useCallback(() => {
    console.log('CommonFeedbackModal: Closing modal without submitting feedback');

    // IMMEDIATELY stop all Common Feedback audio - SYNCHRONOUSLY
    console.log('CommonFeedbackModal: IMMEDIATELY stopping all Common Feedback audio');
    try {
      // Import synchronously to ensure immediate execution
      const audioUtils = require('../utils/audioStreamingStatic');

      // 1. Immediately mute gain node
      audioUtils.setAudioVolume(0);
      console.log('CommonFeedbackModal: Immediately muted gain node');

      // 2. Clear audio queue and stop sources
      audioUtils.clearAudioQueue();
      console.log('CommonFeedbackModal: Cleared audio queue on close');

      // 3. Force stop assistant speaking state
      const audioUtilsModule = audioUtils as any;
      if (audioUtilsModule.isAssistantSpeaking !== undefined) {
        audioUtilsModule.isAssistantSpeaking = false;
      }
      console.log('CommonFeedbackModal: Force stopped assistant speaking state');
    } catch (err) {
      console.error('CommonFeedbackModal: Error clearing audio:', err);
      // Fallback to async import if synchronous fails
      try {
        import('../utils/audioStreamingStatic').then(audioUtils => {
          audioUtils.setAudioVolume(0);
          audioUtils.clearAudioQueue();
          const audioUtilsModule = audioUtils as any;
          if (audioUtilsModule.isAssistantSpeaking !== undefined) {
            audioUtilsModule.isAssistantSpeaking = false;
          }
        });
      } catch (fallbackErr) {
        console.error('CommonFeedbackModal: Fallback audio clearing also failed:', fallbackErr);
      }
    }

    // Send cancel message to WebSocket if connected, then disconnect completely
    if (assistantChatRef.current && assistantChatRef.current.assistantSocket) {
      try {
        const socket = assistantChatRef.current.assistantSocket;
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'cancel_feedback',
            timestamp: Date.now(),
            action: 'cancel'
          }));
          console.log('CommonFeedbackModal: Feedback cancellation message sent to WebSocket');
        }
      } catch (err) {
        console.error('Error sending feedback cancellation message:', err);
      }

      // Completely disconnect the Common Feedback WebSocket
      try {
        console.log('CommonFeedbackModal: Completely disconnecting Common Feedback WebSocket');
        assistantChatRef.current.endConversation();
      } catch (err) {
        console.error('CommonFeedbackModal: Error disconnecting WebSocket:', err);
      }
    }

    onClose();
  }, [onClose]);

  // Handle feedback submission
  const handleSubmitFeedback = useCallback(async () => {
    try {
      setIsSubmitting(true);
      console.log('CommonFeedbackModal: Submitting feedback');

      // IMMEDIATELY stop all Common Feedback audio before submission - SYNCHRONOUSLY
      console.log('CommonFeedbackModal: IMMEDIATELY stopping all Common Feedback audio on submit');
      try {
        // Import synchronously to ensure immediate execution
        const audioUtils = require('../utils/audioStreamingStatic');

        // 1. Immediately mute gain node
        audioUtils.setAudioVolume(0);
        console.log('CommonFeedbackModal: Immediately muted gain node on submit');

        // 2. Clear audio queue and stop sources
        audioUtils.clearAudioQueue();
        console.log('CommonFeedbackModal: Cleared audio queue on submit');

        // 3. Force stop assistant speaking state
        const audioUtilsModule = audioUtils as any;
        if (audioUtilsModule.isAssistantSpeaking !== undefined) {
          audioUtilsModule.isAssistantSpeaking = false;
        }
        console.log('CommonFeedbackModal: Force stopped assistant speaking state on submit');
      } catch (err) {
        console.error('CommonFeedbackModal: Error clearing audio on submit:', err);
        // Fallback to async import if synchronous fails
        try {
          import('../utils/audioStreamingStatic').then(audioUtils => {
            audioUtils.setAudioVolume(0);
            audioUtils.clearAudioQueue();
            const audioUtilsModule = audioUtils as any;
            if (audioUtilsModule.isAssistantSpeaking !== undefined) {
              audioUtilsModule.isAssistantSpeaking = false;
            }
          });
        } catch (fallbackErr) {
          console.error('CommonFeedbackModal: Fallback audio clearing also failed:', fallbackErr);
        }
      }

      // Send feedback submission message to the WebSocket
      if (assistantChatRef.current && assistantChatRef.current.assistantSocket) {
        try {
          const socket = assistantChatRef.current.assistantSocket;
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
              type: 'submit_feedback',
              timestamp: Date.now(),
              action: 'submit'
            }));
            console.log('CommonFeedbackModal: Feedback submission message sent to WebSocket');
          }
        } catch (err) {
          console.error('Error sending feedback submission message:', err);
        }
      }

      // Send voice logs before finishing
      try {
        const { endVoiceLoggingSession } = await import('../utils/voiceLogging');
        await endVoiceLoggingSession('common_feedback_complete');
      } catch (err) {
        console.log('Error ending voice logging session:', err);
      }

      // Close Common Feedback WebSocket connection
      if (assistantChatRef.current) {
        assistantChatRef.current.endConversation();
      }

      // Call completion callback
      if (onSubmitComplete) {
        onSubmitComplete();
      }

      // Close modal
      handleClose();

    } catch (error) {
      console.error('Error submitting feedback:', error);
      setError('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [onSubmitComplete, handleClose]);

  // Don't render if modal is not open
  if (!isOpen) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4"
      style={{
        background: "rgba(30, 41, 59, 0.25)",
        backdropFilter: "blur(8px) saturate(180%)",
        WebkitBackdropFilter: "blur(8px) saturate(180%)"
      }}
    >
      <div className="bg-white backdrop-blur-md rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto border border-gray-200">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">
              {t('common_feedback_title')}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 transition-colors rounded-full p-1 hover:bg-gray-200/60 focus:outline-none focus:ring-2 focus:ring-blue-400"
              disabled={isSubmitting}
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {/* Avatar and controls */}
          <div className="flex flex-col justify-center items-center">
            {/* Avatar display */}
            <div className="flex justify-center items-center mb-4 relative w-full">
              <div className="relative mx-auto w-40 h-40 sm:w-48 sm:h-48">
                <DynamicAvatar
                  gender={avatarGender}
                  isActive={isConnected}
                  isSpeaking={isSpeaking}
                  audioLevel={audioLevel}
                />
              </div>
            </div>

            {/* WebSocket controls */}
            <WebSocketControls
              lng={lng}
              isRecording={isRecording}
              isConnected={isConnected}
              isPaused={isPaused}
              onMicClick={() => {
                assistantHandlers.handleMicClick(
                  assistantChatRef,
                  isRecording,
                  isUnmountingRef,
                  'CommonFeedbackModal',
                  setIsRecording
                );
              }}
              onStopClick={() => {
                assistantHandlers.handleStopClick(
                  assistantChatRef,
                  isPaused,
                  isUnmountingRef,
                  'CommonFeedbackModal',
                  setIsPaused
                );
              }}
              onReconnectClick={() => {
                assistantHandlers.handleReconnectClick(
                  assistantChatRef,
                  isPaused,
                  isUnmountingRef,
                  'CommonFeedbackModal',
                  setIsPaused
                );
              }}
            />

            {/* Submit button */}
            <div className="mt-6 w-full">
              <button
                onClick={handleSubmitFeedback}
                disabled={isSubmitting}
                className={`w-full py-3 px-4 rounded-lg font-semibold shadow transition-all duration-200
                  ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-[#52bcc3] to-[#3da8af] hover:from-[#3da8af] hover:to-[#52bcc3] cursor-pointer'
                  }
                  text-white
                `}
              >
                {isSubmitting ? t('submitting') : t('submit_feedback')}
              </button>
            </div>
          </div>

          {/* Hidden AssistantChat component */}
          <div className="h-0 overflow-hidden">
            <AssistantChat
              ref={(ref) => {
                assistantChatRef.current = ref;
                if (ref && 'isPaused' in ref) {
                  const newPausedState = ref.isPaused;
                  if (isPaused !== newPausedState) {
                    setIsPaused(newPausedState);
                  }
                }
              }}
              userToken={localStorage.getItem(STORAGE_KEYS.TOKEN) || ''}
              avatarGender={avatarGender}
              agentRole={AgentRoleEnum.CM_FA}
              language={user?.details?.language || lng}
              onConversationComplete={() => {
                console.log('CommonFeedbackModal: Conversation completed');
              }}
              onRecordingStateChange={(recording) => {
                setIsRecording(recording);
              }}
              onConnectionStateChange={(connected) => {
                setIsConnected(connected);
                if (!connected) {
                  setIsPaused(false);
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
